from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

# 角色基础模式
class RoleBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    status: Optional[int] = 1

# 创建角色
class RoleCreate(RoleBase):
    pass

# 更新角色
class RoleUpdate(RoleBase):
    name: Optional[str] = None
    code: Optional[str] = None

# 数据库中的角色
class RoleInDBBase(RoleBase):
    id: int
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 返回给客户端的角色
class Role(RoleInDBBase):
    pass

# 权限基础模式
class PermissionBase(BaseModel):
    name: str
    code: str
    type: Optional[str] = "menu"
    parent_id: Optional[int] = 0
    path: Optional[str] = None
    component: Optional[str] = None
    icon: Optional[str] = None
    sort: Optional[int] = 0
    status: Optional[int] = 1

# 创建权限
class PermissionCreate(PermissionBase):
    pass

# 更新权限
class PermissionUpdate(PermissionBase):
    name: Optional[str] = None
    code: Optional[str] = None

# 数据库中的权限
class PermissionInDBBase(PermissionBase):
    id: int
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 返回给客户端的权限
class Permission(PermissionInDBBase):
    pass

# 角色权限关联
class RolePermissionBase(BaseModel):
    role_id: int
    permission_id: int

class RolePermissionCreate(RolePermissionBase):
    pass

class RolePermission(RolePermissionBase):
    id: int
    create_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 用户角色关联
class UserRoleBase(BaseModel):
    user_id: int
    role_id: int

class UserRoleCreate(UserRoleBase):
    pass

class UserRole(UserRoleBase):
    id: int
    create_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 角色权限分配
class RolePermissionAssign(BaseModel):
    role_id: int
    permission_ids: List[int]

# 用户角色分配
class UserRoleAssign(BaseModel):
    user_id: int
    role_ids: List[int]
