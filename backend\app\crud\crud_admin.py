from sqlalchemy.orm import Session
from app.models.admin import SysAdmin
from app.schemas.admin import AdminCreate
from app.core.security import get_password_hash

def get_admin_by_username(db: Session, *, username: str) -> SysAdmin:
    return db.query(SysAdmin).filter(SysAdmin.username == username).first()

def create_admin(db: Session, *, obj_in: AdminCreate) -> SysAdmin:
    db_obj = SysAdmin(
        username=obj_in.username,
        email=obj_in.email,
        real_name=obj_in.real_name,
        password=get_password_hash(obj_in.password), # Hash password before saving
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj 