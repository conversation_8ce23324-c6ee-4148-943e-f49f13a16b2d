#!/usr/bin/env python3
"""
初始化管理员账户脚本
运行此脚本来创建默认的管理员账户
"""

from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app.models import SysAdmin
from app.core.security import get_password_hash
from app.models import Base

def init_admin():
    # 创建数据库表（如果不存在）
    Base.metadata.create_all(bind=engine)
    
    # 创建数据库会话
    db: Session = SessionLocal()
    
    try:
        # 检查是否已存在admin用户
        existing_admin = db.query(SysAdmin).filter(SysAdmin.username == "admin").first()
        
        if existing_admin:
            print("管理员账户已存在，更新密码...")
            existing_admin.password = get_password_hash("123456")
            db.commit()
            print("管理员密码已更新为: 123456")
        else:
            print("创建新的管理员账户...")
            # 创建管理员账户
            admin_user = SysAdmin(
                username="admin",
                password=get_password_hash("123456"),
                real_name="系统管理员",
                email="<EMAIL>",
                status=1,
                deleted=0
            )
            
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            
            print(f"管理员账户创建成功!")
            print(f"用户名: admin")
            print(f"密码: 123456")
            print(f"用户ID: {admin_user.id}")
            
    except Exception as e:
        print(f"创建管理员账户时出错: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_admin()
