import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'
import { useUserStore } from '@/store/modules/user'
import { getToken } from '@/utils/auth'

// 在routes数组中添加更多路由
const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue')
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: { title: '首页', icon: 'House' },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表盘', icon: 'DataLine' }
      }
    ]
  },
  // 添加更多菜单项
  {
    path: '/system',
    component: Layout,
    redirect: '/system/users',
    meta: { title: '系统管理', icon: 'Setting' },
    children: [
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/system/users/index.vue'),
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: 'roles',
        name: 'Roles',
        component: () => import('@/views/system/roles/index.vue'),
        meta: { title: '角色管理', icon: 'UserFilled' }
      }
    ]
  },
  // 添加数据管理菜单
  {
    path: '/data',
    component: Layout,
    redirect: '/data/list',
    meta: { title: '数据管理', icon: 'Document' },
    children: [
      {
        path: 'list',
        name: 'DataList',
        component: () => import('@/views/dashboard/index.vue'), // 临时使用仪表盘组件
        meta: { title: '数据列表', icon: 'List' }
      },
      {
        path: 'analysis',
        name: 'DataAnalysis',
        component: () => import('@/views/dashboard/index.vue'), // 临时使用仪表盘组件
        meta: { title: '数据分析', icon: 'PieChart' }
      }
    ]
  },
  // 添加工具菜单
  {
    path: '/tools',
    component: Layout,
    redirect: '/tools/export',
    meta: { title: '系统工具', icon: 'Tools' },
    children: [
      {
        path: 'export',
        name: 'Export',
        component: () => import('@/views/dashboard/index.vue'), // 临时使用仪表盘组件
        meta: { title: '数据导出', icon: 'Download' }
      },
      {
        path: 'import',
        name: 'Import',
        component: () => import('@/views/dashboard/index.vue'), // 临时使用仪表盘组件
        meta: { title: '数据导入', icon: 'Upload' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

const whiteList = ['/login'] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  const userStore = useUserStore()
  // 同时检查cookie和localStorage中的token
  const hasToken = getToken() || localStorage.getItem('token')

  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
    } else {
        next()
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
    }
  }
})

export default router