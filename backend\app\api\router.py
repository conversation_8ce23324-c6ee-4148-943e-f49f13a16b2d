from fastapi import APIRouter
from app.api.endpoints import auth, dashboard, charity, question, news

# Import your future endpoint routers here
# from .endpoints import auth, users, ...

api_router = APIRouter()

# Include future routers here
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
api_router.include_router(charity.router, prefix="/charity", tags=["charity"])
api_router.include_router(question.router, prefix="/questions", tags=["questions"])
api_router.include_router(news.router, prefix="/news", tags=["news"])
# api_router.include_router(users.router, prefix="/users", tags=["users"])