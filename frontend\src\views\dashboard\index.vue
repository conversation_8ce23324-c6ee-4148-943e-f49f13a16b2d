<template>
  <div class="dashboard-container">
    <el-card class="welcome-card">
      <template #header>
        <div class="card-header">
          <h3>欢迎使用管理系统</h3>
        </div>
      </template>
      <div v-if="loading" class="dashboard-content">
        <p>加载中...</p>
      </div>
      <div v-else class="dashboard-content">
        <p>当前登录用户: <strong>{{ userStore.username }}</strong></p>
        <p>登录状态: <el-tag type="success" v-if="userStore.isLoggedIn">已登录</el-tag></p>
        <p>系统时间: {{ currentTime }}</p>
      </div>
    </el-card>
    
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>系统状态</h3>
            </div>
          </template>
          <div v-if="loading" class="card-content">
            <p>加载中...</p>
          </div>
          <div v-else class="card-content">
            <el-progress type="dashboard" :percentage="dashboardData.systemStatus" :color="statusColors" />
            <p class="stat-label">系统运行正常</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>今日访问</h3>
            </div>
          </template>
          <div v-if="loading" class="card-content">
            <p>加载中...</p>
          </div>
          <div v-else class="card-content">
            <div class="stat-number">{{ dashboardData.todayVisits }}</div>
            <p class="stat-label">访问人次</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>待处理事项</h3>
            </div>
          </template>
          <div v-if="loading" class="card-content">
            <p>加载中...</p>
          </div>
          <div v-else class="card-content">
            <div class="stat-number">{{ dashboardData.pendingTasks }}</div>
            <p class="stat-label">待处理任务</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { getDashboardData } from '@/api/dashboard'

const userStore = useUserStore()
const currentTime = ref(new Date().toLocaleString())
const loading = ref(true)
const dashboardData = reactive({
  systemStatus: 0,
  todayVisits: 0,
  pendingTasks: 0
})

// 状态颜色
const statusColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 }
]

// 更新时间
let timer: number
onMounted(async () => {
  try {
    const response = await getDashboardData()
    if (response && response.data) {
      dashboardData.systemStatus = response.data.systemStatus
      dashboardData.todayVisits = response.data.todayVisits
      dashboardData.pendingTasks = response.data.pendingTasks
    }
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error)
  } finally {
    loading.value = false
  }

  timer = window.setInterval(() => {
    currentTime.value = new Date().toLocaleString()
  }, 1000)
})

onUnmounted(() => {
  clearInterval(timer)
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-content {
  line-height: 2;
  font-size: 16px;
}

.stat-cards {
  margin-top: 20px;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 10px;
}
</style>