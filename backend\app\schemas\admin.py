from pydantic import BaseModel
from typing import Optional

# Shared properties
class AdminBase(BaseModel):
    username: str
    email: Optional[str] = None
    real_name: Optional[str] = None

# Properties to receive on item creation
class AdminCreate(AdminBase):
    password: str

# Properties to receive on item update
class AdminUpdate(AdminBase):
    pass

# Properties shared by models stored in DB
class AdminInDBBase(AdminBase):
    id: int
    status: int

    class Config:
        orm_mode = True

# Properties to return to client
class Admin(AdminInDBBase):
    pass

# Properties stored in DB
class AdminInDB(AdminInDBBase):
    hashed_password: str 